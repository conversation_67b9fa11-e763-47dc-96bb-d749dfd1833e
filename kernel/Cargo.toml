[package]
name = "mork"
version = "0.1.0"
edition = "2021"

# [build]
# rustflags = "-C opt-level=3 -C target-cpu=native"

[dependencies]
log = { workspace=true }
env_logger = { workspace=true }
pathmap = { workspace = true, features = [] }
mork-frontend = { workspace=true }
mork-bytestring = { workspace=true }
bucket_map = { workspace = true }
memmap2 = "0.9.5"
#neo4rs = { version = "0.9.0-rc.5" , features = ["unstable-bolt-protocol-impl-v2"]}
#tokio = "1.43.0"
uuid = { version = "1.14.0" , features = ["v4", "fast-rng"] }
futures = "0.3.31"
freeze = { workspace = true }
neo4rs = { version = "0.9.0-rc.6", optional = true }
tokio = { version = "1.44.0", optional = true }

[features]
default = []
neo4j = ["dep:neo4rs", "dep:tokio"]
interning = []
