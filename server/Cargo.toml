[package]
name = "mork_server"
version = "0.1.0"
edition = "2021"

[dependencies]
pathmap = { workspace=true }
mork = { workspace=true }
mork-frontend = { workspace=true }
mork-bytestring = { workspace = true }
bucket_map = { workspace=true }
hyper = { version = "1", features = ["full"] }
tokio = { version = "1", features = ["full"] }
bytes = { version = "1" }
http-body-util = "0.1"
hyper-util = { version = "0.1", features = ["full"] }
url = { version = "2.5" }
urlencoding = "2.1.3"
reqwest = { version = "0.12.12", features = ["gzip", "deflate", "stream"] }
gxhash = "3.4.1"
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.138"
scc = { version = "2.3.0", optional = true }
num_cpus = { version = "1.16.0", optional = true }
tokio-stream = { version = "0.1.17", features = ["sync"] }
futures-util = { version="0.3.30" }

[dev-dependencies]
eventsource-stream = "0.2" # Used to parse server-side-events for the `status_listen` tests

[features]
default = ["tokio_workers", "neo4j", "local_files"]
serialize_tests = [] # Causes tests to run with 100ms between each one, so they don't interfere with each other
tokio_workers = [] # Use Tokio threads as the server workers
mork_workers = ["dep:num_cpus", "dep:scc"] # Use our own custom thread pool for CPU-heavy work.  CURRENTLY UNMAINTAINED
local_files = [] # Allows the server to import and export files on the host.  Handy for a local server, but disable in a multi-user remote environment with untrusted users.
neo4j = ["mork/neo4j"]
interning = ["mork/interning"]

