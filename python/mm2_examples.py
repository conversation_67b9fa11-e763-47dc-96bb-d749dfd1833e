#!/usr/bin/env python3
"""
MM2 Examples - Demonstrating MM2 programming with MORK server

This script contains several runnable examples showing how to:
1. Basic pattern matching and transformation
2. Use exec for inference
3. Multi-step processing with def
4. Process calculus operations
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from client import ManagedMORK
import time

def example_1_basic_transform():
    """Example 1: Basic Pattern Matching and Transformation"""
    print("\n" + "="*60)
    print("Example 1: Basic Pattern Matching and Transformation")
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        # Upload some data
        print("\n1. Uploading data...")
        server.upload_("""
            (person John programmer)
            (person Jane designer) 
            (person Bob programmer)
            (salary John 100000)
            (salary Jane 90000)
            (salary Bob 95000)
        """)
        
        # Show uploaded data
        print("\nUploaded data:")
        print(server.download_().data)
        
        # Transform: Find all programmers and their salaries
        print("\n2. Finding programmers and their salaries...")
        server.transform(
            patterns=("(person $name programmer)", "(salary $name $amount)"),
            templates=("(programmer-salary $name $amount)",)
        ).block()
        
        # Download results
        result = server.download("(programmer-salary $x $y)", "(programmer-salary $x $y)")
        print("\nProgrammer salaries found:")
        print(result.data)

def example_2_aunt_inference():
    """Example 2: Family Relationship Inference with exec (from MM2 spec)"""
    print("\n" + "="*60)
    print("Example 2: Family Relationship Inference") 
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        # Upload family tree data exactly as in the PDF
        print("\n1. Uploading family tree data...")
        server.upload_("""
            (parent Tom Bob)
            (parent Pam Bob)
            (parent Tom Liz)
            (parent Bob Ann)
            (parent Bob Pat)
            (parent Pat Jim)
            (male Tom)
            (male Bob)
            (male Jim)
            (female Pam)
            (female Liz)
            (female Pat)
            (female Ann)
            (poi Jim)
            (poi Ann)
        """)
        
        # Define aunt-finding rule using exec with proper (location priority) format
        print("\n2. Defining aunt inference rule (from PDF)...")
        # The exec format requires (location priority) not just location
        aunt_rule = """
(exec (aunt_inference 0)
    (, (poi $x)
       (parent $p $x) 
       (parent $gp $p) 
       (parent $gp $y)
       (female $y))
    (, (COMP FALSE $p $y))
    (, ($y aunt of $x)))
        """
        
        server.upload_(aunt_rule)
        
        # Execute the inference
        print("\n3. Running inference...")
        server.exec(thread_id="aunt_inference").block()
        
        # Get results
        aunts = server.download("($y aunt of $x)", "($y aunt of $x)")
        print("\nAunt relationships found:")
        print(aunts.data)
        
        # Expected output according to PDF: 
        # (Liz aunt of Ann)
        # (Ann aunt of Jim)

def example_3_counter():
    """Example 3: Simple Counter using exec"""
    print("\n" + "="*60)
    print("Example 3: Simple Counter")
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        with server.work_at("counter").and_clear() as counter:
            # Initialize counter
            print("\n1. Initializing counter...")
            counter.upload_("""
                (count 0)
            """)
            
            # Define increment rule with proper (location priority) format
            print("\n2. Defining increment rule...")
            increment_rule = """
            (exec (counter_inc 0)
                (, (count $n))
                (, (INCR $n $n_plus_1))
                (, (count $n_plus_1)))
            """
            
            counter.upload_(increment_rule)
            
            # Run increment multiple times
            print("\n3. Running increments...")
            for i in range(3):
                print(f"   Increment {i+1}...")
                counter.exec(thread_id="counter_inc").block()
                result = counter.download("(count $x)", "count: $x")
                print(f"   Current {result.data.strip()}")

def example_4_multi_step_processor():
    """Example 4: Multi-step Processing with def"""
    print("\n" + "="*60)
    print("Example 4: Multi-step Processing with def")
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        with server.work_at("processor").and_clear() as proc:
            # Define a two-step processing pipeline
            print("\n1. Defining multi-step processor...")
            processor = """
            (def data_processor 0
                 (, (raw-data $x)
                    (def data_processor 1 $p $t))
                 (, (exec (data_processor temp) (, (step1 $x)) (,))
                    (exec (data_processor continue) $p $t)
                    (intermediate $x)))
            
            (def data_processor 1
                 (, (intermediate $x)
                    (exec (data_processor temp) (, (step1 $x)) (,)))
                 (, (processed $x)))
            """
            
            proc.upload_(processor)
            
            # Upload raw data
            print("\n2. Uploading raw data...")
            proc.upload_("""
                (raw-data value1)
                (raw-data value2)
                (raw-data value3)
            """)
            
            # Start processing with initial exec
            print("\n3. Starting processor...")
            initial_exec = """
            (exec (data_processor main)
                  (, (def data_processor 0 $p $t))
                  (, (exec (data_processor main) $p $t)))
            """
            proc.upload_(initial_exec)
            
            # Run the processor
            proc.exec(thread_id="data_processor").block()
            proc.exec(thread_id="(data_processor temp)").block()
            
            # Get results
            print("\n4. Results:")
            intermediate = proc.download("(intermediate $x)", "intermediate: $x")
            print("Intermediate data:")
            print(intermediate.data)
            
            processed = proc.download("(processed $x)", "processed: $x")
            print("\nProcessed data:")
            print(processed.data)

def example_5_conditional_logic():
    """Example 5: Conditional Logic with Grounded Operations"""
    print("\n" + "="*60)
    print("Example 5: Conditional Logic")
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        with server.work_at("logic").and_clear() as logic:
            # Upload test data
            print("\n1. Uploading test data...")
            logic.upload_("""
                (score Alice 85)
                (score Bob 70)
                (score Charlie 95)
                (score Diana 60)
            """)
            
            # Define grading rule with conditional logic
            print("\n2. Defining grading rules...")
            grading_rule = """
            (exec grader
                (, (score $student $points))
                (, (GTE $points 90 $is_a)
                   (ITE $is_a (True False) 
                        ((grade A) (grade B))
                        (($grade_a $_) ($_ $grade_other))))
                (, (result $student $grade_a)))
            """
            
            # Note: This is a simplified example. Real conditional logic
            # would need proper grounded operation support
            
            # For demonstration, use simpler pattern-based approach
            simple_grading = """
            (exec (simple_grader 0)
                (, (score $student $points))
                ()
                (, (grade $student $points)))
            """
            
            logic.upload_(simple_grading)
            logic.exec(thread_id="simple_grader").block()
            
            # Get results
            grades = logic.download("(grade $s $p)", "(grade $s $p)")
            print("\nGrades assigned:")
            print(grades.data)

def example_6_data_aggregation():
    """Example 6: Data Aggregation"""
    print("\n" + "="*60)
    print("Example 6: Data Aggregation")
    print("="*60)
    
    with ManagedMORK.connect("../target/release/mork_server").and_terminate() as server:
        with server.work_at("sales").and_clear() as sales:
            # Upload sales data
            print("\n1. Uploading sales data...")
            sales.upload_("""
                (sale electronics laptop 1200)
                (sale electronics phone 800)
                (sale electronics tablet 500)
                (sale clothing shirt 50)
                (sale clothing pants 80)
                (sale clothing jacket 150)
                (sale food apple 2)
                (sale food bread 3)
                (sale food milk 4)
            """)
            
            # Find all electronics sales
            print("\n2. Finding electronics sales...")
            sales.transform(
                patterns=("(sale electronics $item $price)",),
                templates=("(electronics-total $item $price)",)
            ).block()
            
            electronics = sales.download("(electronics-total $i $p)", "(electronics-total $i $p)")
            print("\nElectronics sales:")
            print(electronics.data)
            
            # Group by category
            print("\n3. Grouping by category...")
            sales.transform(
                patterns=("(sale $category $item $price)",),
                templates=("(category-item $category ($item $price))",)
            ).block()
            
            categories = sales.download("(category-item $c $data)", "(category-item $c $data)")
            print("\nCategorized items:")
            print(categories.data)

def main():
    """Run all examples"""
    print("\n" + "="*60)
    print("MM2 Programming Examples with MORK Server")
    print("="*60)
    
    examples = [
        ("Basic Transform", example_1_basic_transform),
        ("Aunt Inference", example_2_aunt_inference),
        ("Counter", example_3_counter),
        ("Multi-step Processor", example_4_multi_step_processor),
        ("Conditional Logic", example_5_conditional_logic),
        ("Data Aggregation", example_6_data_aggregation)
    ]
    
    print("\nAvailable examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    choice = input("\nEnter example number to run (1-6), or 'all' for all examples: ")
    
    if choice.lower() == 'all':
        for name, func in examples:
            try:
                func()
                time.sleep(1)  # Brief pause between examples
            except Exception as e:
                print(f"\nError in {name}: {e}")
    else:
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(examples):
                examples[idx][1]()
            else:
                print("Invalid example number")
        except ValueError:
            print("Invalid input")

if __name__ == "__main__":
    main()
