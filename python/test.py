from client import ManagedMORK

def load_1():

    with ManagedMORK.connect("../target/release/mork_server").and_terminate().and_log_stderr() as server:

        with server.work_at("main").and_clear() as ins:

            ins.upload_("(foo 1)\n(foo 2)\n")
            ins.upload_("(bar 1)\n(bar 2)\n")
            ins.upload_("(baz 3)\n(baz 4)\n")

            ins.transform(("(foo $i)", "($x $i)", "($y $i)"), ("(tmp $x)", ("(tmp $y)"))).block()
            print("query: ", ins.query(("(foo $x)",)).block())

            with ins.work_at("tmp") as tmp:
                # tmp.query(("$x",)).block()
                dwd = tmp.download("($x)", "main $x")
                print("tmp:\n", dwd.data)

            # sleep(1)
            print("server data:\n", ins.download_().data)

def load_2():

    with ManagedMORK.connect("../target/release/mork_server").and_terminate().and_log_stderr().and_log_stdout() as server:
        with server.work_at("main").and_clear() as ins:

            ins.upload_("""(parent Tom Bob)
            (parent Pam Bob)
            (parent Tom Liz)
            (parent Bob Ann)
            (parent Bob Pat)
            (parent Pat Jim)
            (male Tom)
            (male Bob)
            (male Jim)
            (female Pam)
            (female Liz)
            (female Pat)
            (female Ann) 
            (poi Jim)
            (poi Ann)
            (data (poi Jim)) 
            (data (poi Ann))
            (aunt (poi $x) $x $y (result ($y aunt of $x)))
            """)

            # ins.transform(("(poi $x)",
            #               "(parent $p $x)",
            #               "(parent $gp $p)",
            #               "(parent $gp $y)",
            #               "(female $y)"),
            #              ("($y aunt of $x)",)
            #              ).block()

            interpreter = f"""
(exec QA
  (, (aunt $xc $x $y $yt) (data $xc) (exec QA $P $G $T)
     (parent $p $x) (parent $gp $p) (parent $gp $y)
     (female $y))
  (, (COMP FALSE $p $y))
  (, (data $yt) (exec QA $P $G $T)))

            """
            ins.upload_(interpreter)
            ins.exec(thread_id="QA").block()

            ins.query(("($y aunt of $x)",), project=("y",), ortho=False, target="(all created {})").block()
            print("download", ins.download_().data)
            print("download", ins.download("(all created $x)", "$x").data)

def peano(x): return "Z" if x == 0 else f"(S {peano(x - 1)})"

def load_3():

    with ManagedMORK.connect("../target/release/mork_server").and_terminate().and_log_stderr().and_log_stdout() as server:
        with server.work_at("petri") as dish:
            # dish.upload_(petri_dish)
            dish.sexpr_import_("file://" + __file__.rpartition("/")[0] + "/resources/petri_dish.metta").block()
            print("dish: ", dish.download("$x", "$x").data)

        server.sexpr_import_("file://" + __file__.rpartition("/")[0] + "/resources/interpreter.metta").block()
        # server.upload_(interpreter)
        print("before exec ", server.download("$x", "$x").data)
        server.exec(thread_id="process_calculus").block()
        print("after exec ", server.download("$x", "$x").data)
        result = server.download("(petri (! result $x))", "$x").data
        print("\nresult", result)
        assert result == f"{peano(4)}\n"

def load_4():

    with ManagedMORK.connect("../target/debug/mork_server").and_terminate().and_log_stderr().and_log_stdout() as server:
        with server.work_at("ecan") as ins:
            ins.sexpr_import_("file://" + __file__.rpartition("/")[0] + "/resources/ecan.metta").block()

            stimulate = """
            (exec (stimulate ant)
                (, (ant (AV 20 20 0)))
            ) 
            """
            # ins.transform(("(insect $x)",), ("($x)",)).block()
            ins.exec(thread_id="add_atom").block()
            print("ins ", ins.download("$x", "$x").data)
        print("server ", server.download("$x", "$x").data)

def load_5():

    with ManagedMORK.connect("../target/debug/mork_server").and_terminate().and_log_stderr().and_log_stdout() as server:
        with server.work_at("ecan") as ins:
            ins.upload_("(foo 1)\n(bar 2)\n(bar 3)")
        server.upload("(exec $l_p $patterns $templates)", "(exec $l_p $patterns $templates)", "(exec (QA 0) (, (ecan (bar $x)) ) (, (bar has $x)))").block()
        print("server before exec", server.download("$x", "$x").data)
        server.exec(thread_id="QA").block()
        print("server ", server.download("$x", "$x").data)

def load_6():
    with ManagedMORK.connect("../target/debug/mork_server")\
            .and_terminate().and_log_stderr().and_log_stdout() as server:

        server.upload_("""
(exec P0 (, (P $event $prob)) (, (f32.PROBu64 $prob $int)) (, (Distr (SampleP $int $event))))
(exec P0 (, (n-samples $k) (Distr $d)) 
         (, (U64.SAMPLE $k $d (SampleP $_ $s))) 
         (, (Sample $s)))
""").block()

        # Run the QA thread to saturation
        server.exec(thread_id="P0").block()

        # Pull derived facts
        print("derived:",
              server.download("$x", "$x").data)

def load_7():

    with ManagedMORK.connect("../target/debug/mork_server")\
        .and_terminate() as server: #.and_log_stderr().and_log_stdout() as server:

        with server.work_at("adams_hw_data") as ins:

            upload_data = """
T
(foo 1)
(foo 2)
            """
            ins.upload_(upload_data).block()

        print("server1:", server.download("(adams_hw_data $x)","$x").data)

        metta_upload_execs = """
(exec (adams_hw_data 0)   (, (adams_hw_data (foo $x)))   (, (adams_hw_data (bar $x)) )   )
(exec (adams_hw_data 0)   (, (adams_hw_data T))          (, (adams_hw_data ran_exec) )   ) 
    """
        server.upload_(metta_upload_execs).block()

        print("server2:", server.download_().data)
        server.exec(thread_id="adams_hw_data").block()
        print("server3:", server.download_().data)

def load_8():
    with ManagedMORK.connect("../target/debug/mork_server")\
            .and_terminate().and_log_stderr().and_log_stdout() as server:

        metta_upload_execs = "(exec (metta_thread_without_substitution_self_reference 0)   (, (exec (metta_thread_without_substitution_self_reference 0) $t $p)) (, (metta_thread_without_substitution_self_reference ran_exec) )   )"

        server.upload_(metta_upload_execs).block()
        server.exec(thread_id="metta_thread_without_substitution_self_reference").block()

        print(server.download("(metta_thread_without_substitution_self_reference $v)", "(self_reference $v)").data)

def load_9():
    with ManagedMORK.connect("../target/debug/mork_server")\
        .and_terminate() as server:# .and_log_stderr().and_log_stdout() as server:

        upload_payload = """
(val a b)
(val c d)
(val e f)
(val g h)
"""
        server.upload("$v", "((metta_thread_basic in) $v)", upload_payload).block()

        def_upload_payload = """
(def metta_thread_basic 0
     (, ((metta_thread_basic in) (val $x $y))
        (def metta_thread_basic 1 $p $t)
     )
     (, (exec ((metta_thread_basic state) \"cleanup\") (, (val $y $x)) (,))
        (exec (metta_thread_basic \"asap\") $p $t)
        
     )
)

(def metta_thread_basic 1
     (, (exec ((metta_thread_basic state) $p) (, (val $u $v)) (,)) 
     )
     (, ((metta_thread_basic out) (val $u $v))
     )
)
"""
        server.upload("(def $loc $step $p $t)", "(def $loc $step $p $t)", def_upload_payload).block()

        metta_upload_initial_exec = "\n(exec (metta_thread_basic \"asap\")   (, (def metta_thread_basic 0 $p $t) )   (, (exec (metta_thread_basic \"asap\") $p $t) )   )"

        server.upload("(exec ($thread $priority) $patterns $templates)", "(exec ($thread $priority) $patterns $templates)", metta_upload_initial_exec).block()

        server.exec(thread_id="metta_thread_basic").block()

        print(server.download("((metta_thread_basic out) $v)", "$v").data)
        print(server.download_().data)
def load_10():
    with ManagedMORK.connect("../target/debug/mork_server")\
        .and_terminate() as server:

        # Upload family tree data with parent relationships and gender info
        upload_payload = """
(parent Tom Bob)
(parent Tom Liz)
(parent John Bob)
(parent John Mary)
(parent Bob Ann)
(parent Bob Pat)
(parent Liz Jim)
(parent Mary Kate)
(male Tom)
(male John)
(male Bob)
(male Jim)
(female Liz)
(female Mary)
(female Ann)
(female Pat)
(female Kate)
(poi Ann)
(poi Pat)
(poi Jim)
(poi Kate)
"""
        server.upload("$v", "((uncle_finder in) $v)", upload_payload).block()

        # Define uncle-finding logic using multi-step approach
        def_upload_payload = """
(def uncle_finder 0
     (, ((uncle_finder in) (parent $gp $p))
        ((uncle_finder in) (parent $p $child))
        ((uncle_finder in) (poi $child))
        ((uncle_finder in) (male $gp))
        (def uncle_finder 1 $patterns $templates)
     )
     (, (exec ((uncle_finder state) "find_uncles") (, (grandparent $gp $child) (father $p $child)) (,))
        (exec (uncle_finder "process") $patterns $templates)
        ((uncle_finder intermediate) (grandparent $gp $child) (father $p $child))
     )
)

(def uncle_finder 1
     (, (exec ((uncle_finder state) $priority) (, (grandparent $gp $child) (father $father $child)) (,))
        ((uncle_finder in) (parent $gp $uncle))
        ((uncle_finder in) (male $uncle))
     )
     (, (exec ((uncle_finder state) "check_not_father") (, (potential_uncle $uncle $child) (father $father $child)) (,))
        ((uncle_finder potential) (potential_uncle $uncle $child) (father $father $child))
     )
)

(def uncle_finder 2
     (, (exec ((uncle_finder state) $priority) (, (potential_uncle $uncle $child) (father $father $child)) (,))
     )
     (, (COMP FALSE $uncle $father)
     )
     (, ((uncle_finder out) ($uncle uncle of $child))
     )
)
"""
        server.upload("(def $loc $step $p $t)", "(def $loc $step $p $t)", def_upload_payload).block()

        # Bootstrap execution
        metta_upload_initial_exec = """
(exec (uncle_finder "start")
      (, (def uncle_finder 0 $p $t))
      (, (exec (uncle_finder "start") $p $t)))
"""
        server.upload("(exec ($thread $priority) $patterns $templates)", "(exec ($thread $priority) $patterns $templates)", metta_upload_initial_exec).block()

        # Execute the uncle-finding pipeline
        server.exec(thread_id="uncle_finder").block()
        server.exec(thread_id="(uncle_finder state)").block()

        # Get results
        print("Uncle relationships found:")
        uncles = server.download("((uncle_finder out) $relationship)", "$relationship")
        print(uncles.data)
        
        print("\nAll data in space:")
        print(server.download_().data)

if __name__ == '__main__':
    # load_1()
    # load_2()
    # load_3()
    # load_4()
    # load_5()
    # load_6()
    # load_7()
    # load_8()
    #load_9()
    load_10()


