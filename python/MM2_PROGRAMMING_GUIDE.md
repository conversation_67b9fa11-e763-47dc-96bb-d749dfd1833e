# MM2 (Minimal MeTTa 2) Programming Guide for MORK Server

## Table of Contents
1. [Introduction](#introduction)
2. [MM2 Language Syntax](#mm2-language-syntax)
3. [MORK Server API](#mork-server-api)
4. [Python Client Usage](#python-client-usage)
5. [Practical Examples](#practical-examples)
6. [Debugging Guide](#debugging-guide)

## Introduction

MM2 (Minimal MeTTa 2) is a process calculus-based language that operates on hypergraphs, designed to supersede the original Minimal MeTTa with improved performance and parallelism support. MORK (MeTTa Optimal Reduction Kernel) provides a blazing-fast server implementation for executing MM2 programs.

### Key Goals of MM2
- **Performance**: Avoid inherent scalability bottlenecks
- **Parallelism**: Native support for parallel execution
- **Programmable Non-determinism**: Control over non-deterministic execution
- **Inference Control**: Programmable control over inference processes
- **Data Science Friendly**: Suitable for experiments and data processing

## MM2 Language Syntax

### Core Concept: Process Calculus OISC (One Instruction Set Computer)

MM2 has a single core instruction: `exec`

```metta
(exec <system>
  (, <pattern0> ... <patternn>)
  (, <grounded0> ... <groundedg>) 
  (, <template0> ... <templatem>))
```

### 1. The `exec` Expression

The fundamental operation in MM2 that performs pattern matching and template instantiation.

#### Syntax:
```metta
(exec (<location> <priority>) 
      (, <patterns...>)
      (, <grounded_ops...>)  
      (, <templates...>))
```

**Components:**
- **location**: Identifies the execution context/thread (e.g., `QA`, `process_calculus`)
- **priority**: Numeric priority for execution ordering (lower = higher priority)
- **patterns**: Comma-separated list of patterns to match against the space
- **grounded_ops**: Optional grounded operations (built-in functions)
- **templates**: Comma-separated list of templates to instantiate with matched bindings

#### Example:
```metta
(exec (QA 0) 
      (, (parent $p $x) (parent $gp $p) (parent $gp $y) (female $y))
      (, (COMP FALSE $p $y))
      (, ($y aunt of $x)))
```

### 2. The `def` Expression

Used to define reusable execution rules that can be triggered by patterns.

#### Syntax:
```metta
(def <location> <step>
     (, <patterns...>)
     (, <templates...>))
```

#### Example:
```metta
(def metta_thread_basic 0
     (, ((metta_thread_basic in) (val $x $y))
        (def metta_thread_basic 1 $p $t))
     (, (exec ((metta_thread_basic state) "cleanup") (, (val $y $x)) (,))
        (exec (metta_thread_basic "asap") $p $t)
        ((metta_thread_basic out) (val $x $y))))
```

### 3. Pattern Matching

Patterns use variables (prefixed with `$`) to match against data in the space:

- **Variable**: `$x`, `$name` - matches any expression
- **Ground terms**: `foo`, `123`, `"string"` - match exact values
- **Composite patterns**: `(parent $x $y)` - match structured data
- **Wildcards**: `$_` - match but don't bind

### 4. Grounded Operations

Built-in operations that perform computations:

- **COMP**: Comparison operation `(COMP FALSE $x $y)` - ensures x ≠ y
- **PARSEINT**: Parse string to integer
- **COUNT**: Count matching expressions
- **INCR**: Increment a value
- **LT**: Less than comparison
- **ITE**: If-then-else
- **DISTRIBUTE**: Distribute work across threads
- **SAMPLE**: Sampling operations for probability

### 5. Data Representation

Data in MM2 is represented as S-expressions:

```metta
(parent Tom Bob)
(female Ann)
(data (poi Jim))
(val a b)
```

## MORK Server API

### Core Endpoints

#### 1. **POST /transform**
Apply pattern-based transformations to the space.

**Body:**
```metta
(transform (, <patterns>) (, <templates>))
```

**Example:**
```python
server.transform(
    patterns=("(foo $x)", "(bar $x)"),
    templates=("(result $x)",)
)
```

#### 2. **GET /metta_thread**
Execute MM2 code at a specified location/thread.

**Query Parameters:**
- `location`: Thread identifier (required)

**Example:**
```python
server.exec(thread_id="QA")
```

#### 3. **POST /upload**
Upload data directly to the space.

**URL:** `/upload/<pattern>/<template>/`

**Example:**
```python
server.upload("$x", "$x", "(foo 1)\\n(foo 2)")
```

#### 4. **GET /export**
Export data from the space.

**URL:** `/export/<pattern>/<template>/`

**Query Parameters:**
- `format`: Export format (metta, json, csv, raw)
- `max_write`: Maximum items to export
- `uri`: File URI to export to

#### 5. **POST /import**
Import data from a file or URL.

**URL:** `/import/<pattern>/<template>/`

**Query Parameters:**
- `uri`: Source URI (http, https, or file://)
- `format`: Import format

#### 6. **GET /status**
Check the status of operations.

**URL:** `/status/<expression>`

**Response:** JSON with status information

#### 7. **GET /status_stream**
Server-sent events stream for real-time status updates.

#### 8. **POST /clear**
Clear data at a specified path.

**Body:** Expression defining the subspace to clear

#### 9. **POST /stop**
Initiate server shutdown.

**Query Parameters:**
- `wait_for_idle`: Wait for workers to finish

## Python Client Usage

### Installation

```bash
cd /home/<USER>/experments/MORK/python
pip install -r requirements.txt
```

### Basic Connection

```python
from client import MORK, ManagedMORK

# Connect to running server
server = MORK(base_url="http://127.0.0.1:8000")

# Or auto-start server if not running
server = ManagedMORK.connect("../target/release/mork_server")
```

### Working with Scoped Subspaces

```python
with server.work_at("my_workspace") as workspace:
    # All operations are scoped to this namespace
    workspace.upload_("(data 1)\\n(data 2)")
    result = workspace.download_()
    print(result.data)
```

### Key Client Methods

```python
# Upload data
server.upload_(data_string)
server.upload(pattern, template, data)

# Download/query data
server.download_()  # Get everything
server.download(pattern, template, max_results)

# Transform operations
server.transform(patterns, templates)
server.query(patterns, project=variables, ortho=False)

# Import/Export files
server.sexpr_import_(file_uri)
server.sexpr_export_(file_uri)
server.csv_import_(file_uri)

# Execute MM2 threads
server.exec(thread_id="thread_name")

# Clear data
server.clear()

# Check status
cmd = server.transform(...)
cmd.block()  # Wait for completion
cmd.poll()   # Check status
```

## Practical Examples

### Example 1: Simple Pattern Matching and Transformation

```python
from client import ManagedMORK

with ManagedMORK.connect("../target/release/mork_server") as server:
    # Upload some data
    server.upload_("""
        (person John programmer)
        (person Jane designer)
        (person Bob programmer)
    """)
    
    # Transform: Find all programmers
    server.transform(
        patterns=("(person $name programmer)",),
        templates=("(programmer $name)",)
    ).block()
    
    # Download results
    result = server.download("(programmer $x)", "$x")
    print("Programmers:", result.data)  # John\\nBob
```

### Example 2: Aunt Relationship Inference

```python
with server.work_at("family") as family:
    # Upload family data
    family.upload_("""
        (parent Tom Bob) (parent Pam Bob) 
        (parent Tom Liz) (parent Bob Ann) 
        (parent Bob Pat) (parent Pat Jim)
        (male Tom) (male Bob) (male Jim)
        (female Pam) (female Liz) (female Pat) (female Ann)
        (poi Jim) (poi Ann)
    """)
    
    # Define and execute aunt-finding rule
    aunt_rule = """
    (exec QA 
        (, (poi $x) (parent $p $x) (parent $gp $p) 
           (parent $gp $y) (female $y))
        (, (COMP FALSE $p $y))
        (, ($y aunt of $x)))
    """
    
    family.upload_(aunt_rule)
    family.exec(thread_id="QA").block()
    
    # Get results
    aunts = family.download("($y aunt of $x)", "($y aunt of $x)")
    print("Aunts found:", aunts.data)
```

### Example 3: Process Calculus - Addition

```python
# Upload process calculus interpreter
interpreter = """
(exec PC0 (, (petri (? $channel $payload $body))
            (petri (! $channel $payload)))
          (, (petri $body)))

(exec PC1 (, (petri (| $lprocess $rprocess)))
          (, (petri $lprocess) (petri $rprocess)))
"""

# Define Peano arithmetic processes
addition = """
(petri (? (add $ret) ((S $x) $y) 
          (? (add $z) ($x $y) 
             (! $ret (S $z)))))
(petri (? (add $ret) (Z $y) (! $ret $y)))
(petri (! (add result) ((S Z) (S Z))))
"""

server.upload_(interpreter + addition)
server.exec(thread_id="process_calculus").block()

result = server.download("(petri (! result $x))", "$x")
print("Addition result:", result.data)  # (S (S Z)) = 2
```

### Example 4: Multi-step Execution with `def`

```python
# Define a multi-step process
process = """
(def processor 0
     (, (input (data $x))
        (def processor 1 $p $t))
     (, (exec (processor state) (, (process $x)) (,))
        (exec (processor continue) $p $t)
        (intermediate $x)))

(def processor 1
     (, (exec (processor state) (, (process $x)) (,)))
     (, (output (result $x))))

(input (data 42))

(exec (processor asap) 
      (, (def processor 0 $p $t))
      (, (exec (processor asap) $p $t)))
"""

server.upload_(process)
server.exec(thread_id="processor").block()
server.exec(thread_id="(processor state)").block()

results = server.download("(output $x)", "$x")
print("Processed:", results.data)
```

### Example 5: Dynamic Priority Control

```python
dynamic_priority = """
(Blue 0) (Blue 1) (Blue 2)
(Red 3) (Red 4)

(exec controller 
    (, (exec controller $patterns $grounding $rhs))
    (, (COUNT (Red $r) $nred) (COUNT (Blue $b) $nblue)
       (LT $nred $nblue $mostly_blue)
       (ITE $mostly_blue (100 10) (10 100) 
            ($bluepriority $redpriority)))
    (, (exec (worker $bluepriority) 
             (, (Blue $x)) (, (processed blue $x)))
       (exec (worker $redpriority) 
             (, (Red $x)) (, (processed red $x)))
       (exec controller $patterns $grounding $rhs)))
"""

server.upload_(dynamic_priority)
server.exec(thread_id="controller").block()
```

## Debugging Guide

### Debugging Checklist

#### 1. **Syntax Validation**
- [ ] All `exec` expressions have 4 components: exec, (location priority), patterns, templates
- [ ] Patterns and templates are wrapped in `(, ...)`
- [ ] Variables start with `$`
- [ ] Parentheses are balanced
- [ ] Strings are properly quoted

#### 2. **Pattern Matching Issues**
- [ ] Check variable names are consistent between patterns and templates
- [ ] Verify ground terms match exactly (case-sensitive)
- [ ] Ensure patterns exist in the space before execution
- [ ] Use `$_` for wildcards you don't need to bind

#### 3. **Execution Flow**
- [ ] Thread IDs (locations) are unique
- [ ] Priority values are numeric (lower = higher priority)
- [ ] Check `exec` status with `server.exec(...).block()`
- [ ] Verify `def` rules are uploaded before execution

#### 4. **Data Verification**
```python
# Debug: Check what's in the space
print("Current data:", server.download_().data)

# Debug: Check specific pattern
result = server.download("(pattern $x)", "$x")
print(f"Matches for pattern: {result.data}")

# Debug: Check execution status
status = server.exec(thread_id="my_thread")
status.block()  # Wait and check for errors
```

#### 5. **Common Errors and Solutions**

**"Thread is already running at that location"**
- Solution: Use unique thread IDs or clear previous execution

**"Malformed sexpr"**
- Solution: Check parentheses balance and syntax

**"Pattern not found"**
- Solution: Verify data exists with `download_()` before pattern matching

**Timeout/No results**
- Check if exec thread completed: `server.exec(...).block()`
- Verify patterns actually match existing data
- Check grounded operations return expected values

#### 6. **Performance Debugging**
```python
# Use timing
with server.work_at("test").and_time() as timed:
    # Your operations
    pass  # Will print execution time

# Check server logs
with ManagedMORK.connect(...).and_log_stdout().and_log_stderr() as server:
    # Operations
    pass  # Will show server output
```

#### 7. **Status Monitoring**
```python
# Poll for status
cmd = server.transform(...)
while True:
    finished, metadata = cmd.poll()
    if finished:
        print("Status:", metadata)
        break
    time.sleep(0.1)

# Or use blocking wait
cmd.block()

# Check thread execution status
status_response = requests.get(f"{server.base}/status/(exec thread_id)")
print(json.loads(status_response.text))
```

### Best Practices

1. **Use Scoped Workspaces**: Isolate different experiments
   ```python
   with server.work_at("experiment1") as exp1:
       # Work here is isolated
   ```

2. **Clear Before Experiments**: Start with clean state
   ```python
   with server.work_at("test").and_clear() as test:
       # Starts empty
   ```

3. **Validate Incrementally**: Test patterns step by step
   ```python
   # First, upload and check data
   server.upload_(data)
   print(server.download_().data)
   
   # Then test pattern matching
   server.transform(simple_pattern, simple_template).block()
   print(server.download_().data)
   
   # Finally, add complex rules
   ```

4. **Use Meaningful Thread IDs**: Makes debugging easier
   ```python
   server.exec(thread_id="family_inference")  # Clear purpose
   ```

5. **Monitor Resource Usage**: For large operations
   ```python
   # Use server metrics when available
   # Set max_results limits for large exports
   server.download("$x", "$x", max_results=1000)
   ```

## Advanced Topics

### Distributed Execution
MM2 supports distributing work across threads using the `DISTRIBUTE` grounded operation:

```metta
(exec ($thread PR0) 
      (, (fringe active $thread $r $x) ($r $x $y))
      (, (DISTRIBUTE 30 $x $thread $newthread))
      (, (fringe active $newthread $r $y)))
```

### Probabilistic Programming
MM2 includes probabilistic operations:

```metta
(exec P0 
      (, (P $event $prob))
      (, (f32.PROBu64 $prob $int))
      (, (Distr (SampleP $int $event))))
```

### State Management
Use different priority levels and state tracking:

```metta
(exec ((thread_name state) cleanup) ...)
(exec (thread_name asap) ...)  
(exec (thread_name later) ...)
```

## Conclusion

MM2 and MORK provide a powerful framework for hypergraph-based computation with native support for parallelism, non-determinism, and inference control. The combination of a simple core instruction (`exec`) with pattern matching and grounded operations enables complex computations while maintaining performance.

Key takeaways:
- MM2 is built around a single instruction: `exec`
- Pattern matching drives computation
- The MORK server provides high-performance execution
- Python client offers convenient interaction
- Debugging requires systematic checking of syntax, patterns, and execution flow

For more examples, check the test files in:
- `/home/<USER>/experments/MORK/python/mm2Resources/`
- `/home/<USER>/experments/MORK/server/tests/`
