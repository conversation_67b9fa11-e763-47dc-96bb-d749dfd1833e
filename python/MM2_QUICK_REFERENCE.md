# MM2 Quick Reference Card

## Core Syntax

### exec - The Universal Instruction
```metta
(exec (<location> <priority>)   ; Thread ID & priority (lower = higher)
      (, <pattern1> ...)         ; Patterns to match
      (, <grounded_op1> ...)     ; Optional grounded operations  
      (, <template1> ...))       ; Templates to instantiate
```

### def - Define Reusable Rules
```metta
(def <location> <step>           ; Location & step number
     (, <patterns>)              ; When these match...
     (, <templates>))            ; Generate these
```

## Pattern Syntax
- `$var` - Variable binding
- `$_` - Wildcard (match but don't bind)
- `foo` - Ground term (exact match)
- `(parent $x $y)` - Composite pattern

## Common Grounded Operations
| Operation | Description | Example |
|-----------|-------------|---------|
| `COMP` | Comparison | `(COMP FALSE $x $y)` - x ≠ y |
| `INCR` | Increment | `(INCR $n $n_plus_1)` |
| `COUNT` | Count matches | `(COUNT (pattern $x) $count)` |
| `LT` | Less than | `(LT $x $y $result)` |
| `ITE` | If-then-else | `(ITE $cond ($t1 $t2) ($e1 $e2) ($r1 $r2))` |
| `PARSEINT` | Parse integer | `(PARSEINT $str $int)` |
| `DISTRIBUTE` | Distribute work | `(DISTRIBUTE $priority $data $old_thread $new_thread)` |

## Python Client Quick Start

### Connection
```python
from client import ManagedMORK
server = ManagedMORK.connect("../target/release/mork_server")
```

### Basic Operations
```python
# Upload data
server.upload_("(data 1)\\n(data 2)")

# Transform
server.transform(
    patterns=("(data $x)",),
    templates=("(result $x)",)
).block()

# Execute thread
server.exec(thread_id="my_thread").block()

# Download results
result = server.download("(result $x)", "$x")
print(result.data)

# Work in subspace
with server.work_at("workspace") as ws:
    ws.upload_("(local data)")
```

## Common Patterns

### Simple Query
```metta
(exec query (, (person $name programmer))
             ()
             (, (programmer $name)))
```

### Inference Rule
```metta
(exec inference 
      (, (parent $x $y) (parent $y $z))
      ()
      (, (grandparent $x $z)))
```

### Multi-step Process
```metta
(def processor 0
     (, (input $x) (def processor 1 $p $t))
     (, (intermediate $x) 
        (exec processor $p $t)))

(def processor 1
     (, (intermediate $x))
     (, (output $x)))
```

### Conditional Execution
```metta
(exec conditional
      (, (value $x))
      (, (LT $x 10 $is_small))
      (, (category $x $is_small)))
```

## Server Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/transform` | POST | Pattern transformation |
| `/metta_thread?location=<id>` | GET | Execute MM2 thread |
| `/upload/<pattern>/<template>/` | POST | Upload data |
| `/export/<pattern>/<template>/` | GET | Export data |
| `/import/<pattern>/<template>/?uri=<uri>` | POST | Import from file |
| `/status/<expr>` | GET | Check status |
| `/clear` | POST | Clear subspace |

## Debugging Tips

1. **Check data exists**: `server.download_().data`
2. **Verify patterns match**: Test with simpler patterns first
3. **Check thread completion**: `server.exec(...).block()`
4. **Use unique thread IDs**: Avoid conflicts
5. **Monitor status**: Check `/status/(exec <thread_id>)`
6. **Enable logging**: `.and_log_stdout().and_log_stderr()`

## Common Issues

| Issue | Solution |
|-------|----------|
| "Thread already running" | Use unique thread ID |
| "Malformed sexpr" | Check parentheses balance |
| No results | Verify patterns match existing data |
| Timeout | Check exec completed with `.block()` |

## Example: Family Relations

```python
# Data
family_data = """
(parent Tom Bob)
(parent Bob Ann)
(female Ann)
(poi Ann)
"""

# Rule
aunt_rule = """
(exec find_aunt
      (, (poi $x)
         (parent $p $x)
         (parent $gp $p)
         (parent $gp $y)
         (female $y))
      (, (COMP FALSE $p $y))
      (, ($y aunt-of $x)))
"""

# Execute
server.upload_(family_data + aunt_rule)
server.exec(thread_id="find_aunt").block()
result = server.download("($y aunt-of $x)", "($y aunt-of $x)")
```
